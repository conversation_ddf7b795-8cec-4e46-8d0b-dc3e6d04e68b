[Info] [00000000000005D0] [2025-08-01 13:31:42.434478900 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [00000000000005D0] [2025-08-01 13:33:40.781056600 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [00000000000028C0] [2025-08-01 13:36:29.709422100 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [00000000000028C0] [2025-08-01 15:00:13.365508700 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [0000000000003950] [2025-08-01 15:01:07.890485800 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [0000000000003634] [2025-08-01 15:34:41.485482400 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Error] [0000000000003600] [2025-08-01 15:44:13.388969100 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3/desktop.ini", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2\5TK3WNM5WWV7FWW3IKKTUUNCEZW2B24FQ77EWB4MACAS))
[Error] [0000000000003600] [2025-08-01 15:44:13.431485500 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3/desktop.ini", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2\5TK3WNM5WWV7FWW3IKKTUUNCEZW2B24FQ77EWB4MACAS))
[Error] [0000000000003600] [2025-08-01 15:44:13.441094900 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3/desktop.ini", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2\5TK3WNM5WWV7FWW3IKKTUUNCEZW2B24FQ77EWB4MACAS))
[Error] [0000000000003600] [2025-08-01 15:44:13.441338100 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3/desktop.ini", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2\5TK3WNM5WWV7FWW3IKKTUUNCEZW2B24FQ77EWB4MACAS))
[Error] [0000000000002B40] [2025-08-01 15:44:13.441833500 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2))
[Error] [0000000000002B40] [2025-08-01 15:44:13.442755100 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2))
[Error] [0000000000002B40] [2025-08-01 15:44:13.464296900 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2))
[Error] [0000000000002B40] [2025-08-01 15:44:13.464754700 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2))
[Error] [0000000000002B40] [2025-08-01 15:44:13.502654000 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2))
[Error] [0000000000002B40] [2025-08-01 15:44:13.502961300 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3/desktop.ini", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2\5TK3WNM5WWV7FWW3IKKTUUNCEZW2B24FQ77EWB4MACAS))
[Error] [0000000000002B40] [2025-08-01 15:44:13.503114900 UTC] [getattr:34]    Function fails with arguments (path="/user/vm3/desktop.ini", st={st_size=0, st_mode=0, st_nlink=0, st_uid=0, st_gid=0, st_blksize=0, st_blocks=0, st_atim=1970-01-01T00:00:00+00:00, st_mtim=1970-01-01T00:00:00+00:00, st_ctim=1970-01-01T00:00:00+00:00, st_birthtim=1970-01-01T00:00:00+00:00}) with return code -13 because it encounters exception class securefs::WindowsException: error 5 Access is denied. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\Z2EECZCIAUHKR4NHTU2J8WHW4HTHHYAG\RTPDN8P2TBSCBPKYPMK64JKJ3P6D8R2\5TK3WNM5WWV7FWW3IKKTUUNCEZW2B24FQ77EWB4MACAS))
[Info] [0000000000003634] [2025-08-01 15:44:20.608300100 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [0000000000001984] [2025-08-01 15:44:36.191419200 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [0000000000001984] [2025-08-01 15:51:41.469910300 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [0000000000001FE0] [2025-08-01 16:31:29.788374900 UTC] [void __cdecl securefs::full_format::FileTable::init(void):30]    Root directory not initialized, creating... (error 3 The system cannot find the path specified. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\00\00000000000000000000000000000000000000000000000000000000000000.meta)))
[Info] [0000000000001DFC] [2025-08-01 16:33:13.166915700 UTC] [void __cdecl securefs::full_format::FileTable::init(void):30]    Root directory not initialized, creating... (error 3 The system cannot find the path specified. (CreateFileW(path=\\?\F:\VM4\ConfigClient\fs\00\00000000000000000000000000000000000000000000000000000000000000.meta)))
[Info] [0000000000001DFC] [2025-08-01 16:33:13.212321000 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [0000000000001DFC] [2025-08-01 16:33:31.184203000 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
