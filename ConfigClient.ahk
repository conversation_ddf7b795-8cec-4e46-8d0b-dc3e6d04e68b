#SingleInstance Force
#include lib\ip.ahk

SetScrollLockState, On
Run, ResetIP.exe

server_user=admin
server_pass=admin
server_ip=***********
server_port=8080
fallback_shell=explorer
connect_timeout=5
retry_count = 3

; Config server login details
IniRead, server_user, ConfigClient.ini, ConfigClient, server_user, %server_user%
IniRead, server_pass, ConfigClient.ini, ConfigClient, server_pass, %server_pass%
IniRead, server_ip, ConfigClient.ini, ConfigClient, server_ip, %server_ip%
IniRead, server_port, ConfigClient.ini, ConfigClient, server_port, %server_port%
IniRead, fallback_shell, ConfigClient.ini, ConfigClient, fallback_shell, %fallback_shell%
IniRead, connect_timeout, ConfigClient.ini, ConfigClient, connect_timeout, %connect_timeout%
IniRead, retry_count, ConfigClient.ini, ConfigClient, retry_count, %retry_count%

; Get local ethernet info
HWInfo := GetEthernetHWInfo()
HWArray := StrSplit(HWInfo, "|")
Ethernet := Trim(HWArray[1])
Adapater := Trim(HWArray[2])
MAC := Trim(HWArray[3])

RunWait, %comspec% /c bin\curl.exe --retry %retry_count% --connect-timeout %connect_timeout% --user "%server_user%:%server_pass%" "%server_ip%:%server_port%/config?mac=%MAC%" | clip,,hide
RetVal = %clipboard%
if RetVal is not space
{
	Array := StrSplit(RetVal, ",")
	IP := Array[1]
	SUBNET_MASK := Array[2]
	GATEWAY := Array[3]
	DNS := Array[4]
	NAME := Array[5]
	SHELL := Array[6]
	CUSTOM_SHELL := Array[7]
	;Msgbox, %IP%`n%SUBNET_MASK%`n%GATEWAY%`n%DNS%`n%NAME%`n%SHELL%`n%CUSTOM_SHELL%
	FileAppend, %A_Now%: IP-%IP% | MASK-%SUBNET_MASK% | GW-%GATEWAY% | DNS-%DNS% | NAME-%NAME% | SHELL-%SHELL% | CUSTOMSHELL-%CUSTOM_SHELL%`n, ConfigClient.log
	
	If (NAME != A_ComputerName AND NAME != "")
	{
		SplashTextOn,,, Renaming computer name to %NAME% and restarting...
		FileAppend, %A_Now%: Changing computer name to %NAME%`n, ConfigClient.log
		RunWait, powershell -Command Start-Process -Verb RunAs powershell '-Command Rename-Computer -NewName "%NAME%" -Restart',,hide
	}
	
	if (SHELL != "")
	{
		if FileExist("vfs.exe") 
		{
			RunWait, vfs.exe Z4Q02Oeb70ib, , Hide UseErrorLevel
			RetError = %ErrorLevel%
			If RetError
			{
				FileAppend, %A_Now%: VFS Error %RetError%`n, ConfigClient.log	
				SHELL=%fallback_shell%
			}	
		}	
		Else
		{
			FileAppend, %A_Now%: VFS vfs.exe not found`n, ConfigClient.log	
			SHELL=%fallback_shell%
		}
	}
	Else
	{
		if FileExist("vfs.exe") 
			RunWait, vfs.exe, , Hide UseErrorLevel
		
		FileAppend, %A_Now%: No shell!`n, ConfigClient.log
		SHELL=%fallback_shell%
	}
}
Else
{
	if FileExist("vfs.exe") 
		RunWait, vfs.exe, , Hide UseErrorLevel
	
	SplashTextOn,,, Unable to get any data from server!
	FileAppend, %A_Now%: Unable to get any data from server!`n, ConfigClient.log
	SHELL=%fallback_shell%
}	


if GetKeyState("Shift")
	SHELL=explorer

if (SHELL="vm3")
{
	vm3path := "c:\apps\" . A_UserName . "\vm3\vm3.exe"
	if FileExist(vm3path) 
	{
		Run, %vm3path%
		RunWait *RunAs SetIP.exe %IP%
	}
	Else
		Run, explorer.exe
} 
Else if (SHELL="custom") 
{
	if FileExist(CUSTOM_SHELL)
	{
		SplitPath, CUSTOM_SHELL,,CUSTOM_SHELL_DIR
		RunWait *RunAs SetIP.exe %IP%
		Run, %CUSTOM_SHELL%,%CUSTOM_SHELL_DIR%
	}	
	Else
		Run, explorer.exe	
} 
Else
	Run, explorer.exe 


SetScrollLockState, Off
ExitApp