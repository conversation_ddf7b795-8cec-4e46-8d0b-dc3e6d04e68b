---

**ConfigClient Installation and Usage Guide**

1. **Installation**:
   - To install ConfigClient, run the `Install_Shell.bat` script. This will install the software to `C:\apps\ConfigClient` and automatically add `ConfigClient.exe` to the Windows startup folder.

2. **Configuration**:
   - ConfigClient will connect to the `server_ip` specified in the `ConfigClient.ini` file. It will retrieve configuration data from the ConfigServer based on the MAC address, including the following parameters:
     - IP Address
     - Subnet Mask
     - Gateway
     - DNS
     - PC Name
     - Shell (default or custom)
     - Custom Shell

3. **PC Name Validation**:
   - If the PC's current name differs from the `NAME` field specified in the ConfigServer's CSV file, ConfigClient will rename the PC accordingly and prompt for a reboot.

4. **Shell Behavior**:
   - **Explorer Shell**: If the shell name is `explorer`, the IP address will not be changed, and Windows Explorer will be launched.
   - **Custom Shell**: If the shell name is `custom`, the specified shell will be launched, but the IP address will remain unchanged.
   - **VM3 Shell**: If the shell name is `VM3`, ConfigClient will update the IP address as specified in the ConfigServer’s CSV.

5. **Special Launch Behavior**:
   - Upon **ConfigClient launch**, the **ScrollLock LED** will turn on for a few seconds to indicate that the application is starting.
   - To bypass the custom shell and go directly to the Windows desktop, **press the Shift key** during launch. This will skip the custom shell behavior and load the default desktop environment.

6. **Fallback Mode**:
   - If ConfigClient is unable to connect to the ConfigServer, it will launch the fallback shell specified in the `ConfigClient.ini` file.

7. **Verifying ConfigServer Status**:
   - To verify the functionality of the ConfigServer, open a web browser on the client computer and navigate to `http://server_ip:server_port`. 
   - For example: `http://***********:8080`.

---
