﻿#NoEnv  ; Recommended for performance and compatibility with future AutoHotkey releases.
; #Warn  ; Enable warnings to assist with detecting common errors.
SendMode Input  ; Recommended for new scripts due to its superior speed and reliability.
SetWorkingDir %A_ScriptDir%  ; Ensures a consistent starting directory.
#SingleInstance Force
#Include lib\ip.ahk

if Not A_IsAdmin
	Run *RunAs "%A_ScriptFullPath%"

HWInfo := GetEthernetHWInfo()
HWArray := StrSplit(HWInfo, "|")
Ethernet := Trim(HWArray[1])
Adapater := Trim(HWArray[2])
MAC := Trim(HWArray[3])

IPInfo := GetAdapaterIPInfo(Ethernet)
IPArray := StrSplit(IPInfo, "|")
IP := Trim(IPArray[1])
Mask := Trim(IPArray[2])
Gateway := Trim(IPArray[3])
DNS := Trim(IPArray[4])

MsgBox, Ethernet: %Ethernet%`nAdapater: %Adapater%`nMAC: %MAC%`nIP: %IP%`nMask: %Mask%`nGW: %Gateway%`nDNS: %DNS%


ip_array := StrSplit(IP, ".")

net1 := ip_array[1]
net2 := ip_array[2]
net3 := ip_array[3]
net4 := ip_array[4]

NewIP=%net1%.%net2%.100.%net4%

RetVal := SetEthernetIP(Ethernet,NewIP,Mask,Gateway,DNS)
Msgbox %RetVal%

;RetVal := SetEthernetDHCP(Ethernet)
;Msgbox %RetVal%


exitapp
























; ======================================================================
; OLD FUNCTIONS

;Ethernet := GetEthernetName()
;Adapater := GetAdapaterName()
;MAC := StrReplace(GetAdapaterMAC(), "-", "")
;MsgBox, %Ethernet% - %Adapater% - %MAC%
;MsgBox, %ConnectionName% - %NetworkAdapter% - %PhysicalAddress%
;IP := GetAdapaterIP(Adapater)
;SubNetMask := GetAdapaterSubnetMask(Adapater)
;Gateway := GetAdapaterGateway(Adapater)
;DNS := GetAdapaterDNS(Adapater)
;GetAdapaterIPInfo(Ethernet)
;MsgBox, %AdapaterIP% - %AdapaterMask% - %AdapaterGW% - %AdapaterDNS%
;Msgbox, %IPInfo%


GetEthernetName(AdapaterNumber := 1) 
{
	EthernetCount := 0
	RunWait, %comspec% /c getmac /fo csv /v /nh | clip,,hide
	GetMacList := StrReplace(clipboard, """", "")
	Loop, parse, GetMacList, `n, `r
	{
		GetMacItem = %A_LoopField%
		If GetMacItem = space
			Continue
		
		GetMacItemArray := StrSplit(GetMacItem, ",")
		ConnectionName := GetMacItemArray[1]
		NetworkAdapter := GetMacItemArray[2]
		
		if PhysicalAddress = N/A
			Continue
		
		If InStr(ConnectionName, "Ethernet")
			EthernetCount := EthernetCount+1
		Else
			Continue
		
		Ethernet = %ConnectionName%
		
		If EthernetCount = %AdapaterNumber%
			Return  Ethernet
	}
}


GetAdapaterName(AdapaterNumber := 1) 
{
	EthernetCount := 0
	RunWait, %comspec% /c getmac /fo csv /v /nh | clip,,hide
	GetMacList := StrReplace(clipboard, """", "")
	Loop, parse, GetMacList, `n, `r
	{
		GetMacItem = %A_LoopField%
		If GetMacItem = space
			Continue
		
		GetMacItemArray := StrSplit(GetMacItem, ",")
		ConnectionName := GetMacItemArray[1]
		NetworkAdapter := GetMacItemArray[2]
		
		if PhysicalAddress = N/A
			Continue
		
		If InStr(ConnectionName, "Ethernet")
			EthernetCount := EthernetCount+1
		Else
			Continue
		
		Adapater = %NetworkAdapter%
		
		If EthernetCount = %AdapaterNumber%
			Return  Adapater
	}
}


GetAdapaterMAC(AdapaterNumber := 1) 
{
	EthernetCount := 0
	RunWait, %comspec% /c getmac /fo csv /v /nh | clip,,hide
	GetMacList := StrReplace(clipboard, """", "")
	Loop, parse, GetMacList, `n, `r
	{
		GetMacItem = %A_LoopField%
		If GetMacItem = space
			Continue
		
		GetMacItemArray := StrSplit(GetMacItem, ",")
		ConnectionName := GetMacItemArray[1]
		PhysicalAddress := GetMacItemArray[3]
		
		if PhysicalAddress = N/A
			Continue
		
		If InStr(ConnectionName, "Ethernet")
			EthernetCount := EthernetCount+1
		Else
			Continue
		
		MAC = %PhysicalAddress%
		
		If EthernetCount = %AdapaterNumber%
			Return MAC
	}
}

GetAdapaterIP(Adapater)
{
	RunWait, %comspec% /c ipconfig /all | clip,,hide
	ipconfig = %clipboard%
	Loop, parse, ipconfig, `n, `r
	{
		IpConfigLine = %A_LoopField%
		
		If InStr(IpConfigLine, Adapater)
			Match = 1
		
		If Match = 1
		{
			If InStr(IpConfigLine, "IPv4 Address")
			{
				Array := StrSplit(IpConfigLine, ":")
				IP := Array[2]
				Array := StrSplit(IP, "(")
				IP := Array[1]
				Return IP
			}	
		}
	}
}

GetAdapaterSubnetMask(Adapater)
{
	RunWait, %comspec% /c ipconfig /all | clip,,hide
	ipconfig = %clipboard%
	Loop, parse, ipconfig, `n, `r
	{
		IpConfigLine = %A_LoopField%
		
		If InStr(IpConfigLine, Adapater)
			Match = 1
		
		If Match = 1
		{
			If InStr(IpConfigLine, "Subnet Mask")
			{
				Array := StrSplit(IpConfigLine, ":")
				SubnetMask := Array[2]
				Array := StrSplit(SubnetMask, "(")
				SubnetMask := Array[1]
				Return SubnetMask
			}	
		}
	}
}

GetAdapaterGateway(Adapater)
{
	RunWait, %comspec% /c ipconfig /all | clip,,hide
	ipconfig = %clipboard%
	Loop, parse, ipconfig, `n, `r
	{
		IpConfigLine = %A_LoopField%
		
		If InStr(IpConfigLine, Adapater)
			Match = 1
		
		If Match = 1
		{
			If InStr(IpConfigLine, "Default Gateway")
			{
				Array := StrSplit(IpConfigLine, ":")
				Gateway := Array[2]
				Array := StrSplit(Gateway, "(")
				Gateway := Array[1]
				Return Gateway
			}	
		}
	}
}

GetAdapaterDNS(Adapater)
{
	RunWait, %comspec% /c ipconfig /all | clip,,hide
	ipconfig = %clipboard%
	Loop, parse, ipconfig, `n, `r
	{
		IpConfigLine = %A_LoopField%
		
		If InStr(IpConfigLine, Adapater)
			Match = 1
		
		If Match = 1
		{
			If InStr(IpConfigLine, "DNS Servers")
			{
				Array := StrSplit(IpConfigLine, ":")
				DNS := Array[2]
				Array := StrSplit(DNS, "(")
				DNS := Array[1]
				Return DNS
			}	
		}
	}
}

