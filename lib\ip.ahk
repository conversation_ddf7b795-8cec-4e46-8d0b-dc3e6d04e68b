﻿#NoEnv  ; Recommended for performance and compatibility with future AutoHotkey releases.
; #Warn  ; Enable warnings to assist with detecting common errors.
SendMode Input  ; Recommended for new scripts due to its superior speed and reliability.
SetWorkingDir %A_ScriptDir%  ; Ensures a consistent starting directory.

;***** Functions *****

GetAdapaterIPInfo(Adapater)
{
	Global AdapaterIP, AdapaterMask, AdapaterGW, AdapaterDNS
	
	RunWait, %comspec% /c ipconfig /all | clip,,hide
	ipconfig = %clipboard%
	Loop, parse, ipconfig, `n, `r
	{
		IpConfigLine = %A_LoopField%
		
		If InStr(IpConfigLine, Adapater)
			Match = 1
		
		If Match = 1
		{
			If InStr(IpConfigLine, "IPv4 Address")
			{
				Array := StrSplit(IpConfigLine, ":")
				IP := Array[2]
				Array := StrSplit(IP, "(")
				AdapaterIP := Array[1]
			}
			
			If InStr(IpConfigLine, "Subnet Mask")
			{
				Array := StrSplit(IpConfigLine, ":")
				AdapaterMask := Array[2]
			}	
			
			If InStr(IpConfigLine, "Default Gateway")
			{
				Array := StrSplit(IpConfigLine, ":")
				AdapaterGW := Array[2]
			}	
			
			If InStr(IpConfigLine, "DNS Servers")
			{
				Array := StrSplit(IpConfigLine, ":")
				AdapaterDNS := Array[2]
			}
		}
	}
	
	Return AdapaterIP . "|" . AdapaterMask . "|" . AdapaterGW . "|" . AdapaterDNS 	
}

GetEthernetHWInfo(AdapaterNumber := 1) 
{
	Global ConnectionName, NetworkAdapter, PhysicalAddress, EthernetCount
	EthernetCount := 0
	RunWait, %comspec% /c getmac /fo csv /v /nh | clip,,hide
	GetMacList := StrReplace(clipboard, """", "")
	Loop, parse, GetMacList, `n, `r
	{
		GetMacItem = %A_LoopField%
		If GetMacItem = space
			Continue
		
		GetMacItemArray := StrSplit(GetMacItem, ",")
		ConnectionName := GetMacItemArray[1]
		NetworkAdapter := GetMacItemArray[2]
		PhysicalAddress := GetMacItemArray[3]
		
		if PhysicalAddress = N/A
			Continue
		
		If InStr(ConnectionName, "Ethernet")
		{
			EthernetCount := EthernetCount+1
			Return  ConnectionName . "|" . NetworkAdapter . "|" . PhysicalAddress . "|" . EthernetCount
		}	
		Else
			Continue
	}
}

SetEthernetIP(Ethernet,IP_ADDRESS,SUBNET_MASK,GATEWAY := "",DNS1 := "",DNS2 := "")
{
	ERR := 0
	
	RunWait, netsh interface ipv4 set address name="%Ethernet%" static %IP_ADDRESS% %SUBNET_MASK% %GATEWAY%,,hide
	ERR := ERR + ErrorLevel
	
	RunWait, netsh interface ip delete dns name="%Ethernet%" all,,hide
	ERR := ERR + ErrorLevel
	
	if DNS1 is not space
	{
		RunWait, netsh interface ipv4 set dns name="%Ethernet%" static %DNS1%,,hide
		ERR := ERR + ErrorLevel
	}	
	
	if DNS2 is not space
	{
		RunWait, netsh interface ipv4 add dns name="%Ethernet%" %DNS2% index=2,,hide
		ERR := ERR + ErrorLevel
	}	
	
	Return ERR
}

SetEthernetDHCP(Ethernet)
{
	ERR := 0
	
	RunWait, netsh int ip set address name="%Ethernet%" source=dhcp,,hide
	ERR := ERR + ErrorLevel
	
	RunWait, netsh interface ip delete dns name="%Ethernet%" all,,hide
	ERR := ERR + ErrorLevel
	
	Return ERR
}

;=============================================================
