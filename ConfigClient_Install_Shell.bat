@echo off

set "ScriptPath=C:\Progs\ConfigClient"
MD %ScriptPath%\bin

copy /y .\SetIP.exe %ScriptPath%\SetIP.exe
copy /y .\ResetIP.exe %ScriptPath%\ResetIP.exe
copy /y .\ConfigClient.exe %ScriptPath%\ConfigClient.exe
copy /y .\ConfigClient.ini %ScriptPath%\ConfigClient.ini
copy /y .\vfs.exe %ScriptPath%\vfs.exe

copy /y .\bin\curl.exe %ScriptPath%\bin\curl.exe
copy /y .\bin\curl-ca-bundle.crt %ScriptPath%\bin\curl-ca-bundle.crt
copy /y .\bin\libcurl-x64.def %ScriptPath%\bin\libcurl-x64.def
copy /y .\bin\libcurl-x64.dll %ScriptPath%\bin\libcurl-x64.dll

reg add "HKCU\Software\Microsoft\Windows NT\CurrentVersion\Winlogon" /v Shell /t REG_SZ /d %ScriptPath%\ConfigClient.exe /f 


ping ********* -n 6 >nul

