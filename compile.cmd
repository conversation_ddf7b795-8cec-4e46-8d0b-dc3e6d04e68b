@echo off
Echo echo|set /p="Compiling." 
"C:\Program Files\AutoHotkey\Compiler\Ahk2Exe.exe" /base "C:\Program Files\AutoHotkey\Compiler\Unicode 64-bit.bin" /compress 1 /in ".\ConfigClient.ahk" /icon ".\ico\application-x-executable_36271.ico"

Echo echo|set /p="." 
"C:\Program Files\AutoHotkey\Compiler\Ahk2Exe.exe" /base "C:\Program Files\AutoHotkey\Compiler\Unicode 64-bit.bin" /compress 1 /in ".\ResetIP.ahk" /icon ".\ico\application-x-executable_36271.ico"

Echo echo|set /p="." 
"C:\Program Files\AutoHotkey\Compiler\Ahk2Exe.exe" /base "C:\Program Files\AutoHotkey\Compiler\Unicode 64-bit.bin" /compress 1 /in ".\SetIP.ahk" /icon ".\ico\application-x-executable_36271.ico"


echo.

mkdir ..\sBoot\ConfigClient\bin
copy /y ConfigClient.exe ..\sBoot\ConfigClient
copy /y ResetIP.exe ..\sBoot\ConfigClient
copy /y SetIP.exe ..\sBoot\ConfigClient
copy /y ConfigClient_Install_Shell.bat ..\sBoot\ConfigClient
copy /y ConfigClient.txt ..\sBoot\ConfigClient

copy /y ConfigClient.ini ..\sBoot\ConfigClient
copy /y vfs.exe ..\sBoot\ConfigClient
copy /y bin\*.* ..\sBoot\ConfigClient\bin



ping -n 5 127.0.0.1>nul